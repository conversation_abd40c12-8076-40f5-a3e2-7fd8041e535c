

using System;
using System.Linq;

using RxjhServer.GroupQuest;
using static RxjhServer.GroupQuest.GroupQuestEvent;
using HeroYulgang.Helpers;
using RxjhServer.Quest;

namespace RxjhServer;

public partial class Players
{
	public void MissionSystem(byte[] packetData, int packetSize)
	{
		try
        {
            int questId = BitConverter.ToInt16(packetData, 10);
            int step = BitConverter.ToInt16(packetData, 12);
            int option = BitConverter.ToInt16(packetData, 14);
            HeThongNhacNho($"Nhiệm vụ {questId} - {step} - {option}", 10, "Thiên cơ các");
            var success = HandleGroupQuest(questId, step, option);
            if (success) return;

            var quest = QuestClass.GetQuest(questId);
            if (quest == null)
            {
                TaskPromptDataSending(questId, 12, option);
                return;
            }
            HeThongNhacNho($"Xử lý quest: {quest.QuestName} (ID: {questId})", 10, "Thiên cơ các");

            switch (questId)
            {
                case 1000:
                // <PERSON>hi<PERSON><PERSON> vụ nâng cấp bang phái lên cấp 2
                case 1:
                    TaskPromptDataSending(questId, 11, option);
                    break;
                case 2:
                    SetTaskData(questId, option);
                    TaskPromptDataSending(questId, 21, option);
                    break;
                case 3:
                    TaskPromptDataSending(questId, 31, option);
                    break;
                case 4:
                    if (QuestList.ContainsKey(questId))
                    {
                        QuestList.Remove(questId);
                    }
                    TaskPromptDataSending(questId, 41, option);
                    break;
                case 5:
                    TaskPromptDataSending(questId, 51, option);
                    CurrentlyOperatingNPC = 0;
                    CurrentOperationType = 0;
                    break;

                // case 1:
                // case 2:
                // case 3:
                //     _mission.NhiemVu(packetData, packetSize);
                default:
                    try
                    {
                        switch (step)
                        {
                            case 1:
                                TaskPromptDataSending(questId, 12, option);
                                break;
                            case 2:
                                SetTaskData(questId, option);
                                TaskPromptDataSending(questId, 21, option);
                                break;
                            case 3:
                                TaskPromptDataSending(questId, 31, option);
                                break;
                            case 4:
                                if (QuestList.ContainsKey(questId))
                                {
                                    QuestList.Remove(questId);
                                }
                                TaskPromptDataSending(questId, 41, option);
                                break;
                            case 5:
                                TaskPromptDataSending(questId, 51, option);
                                CurrentlyOperatingNPC = 0;
                                CurrentOperationType = 0;
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Error, "MissIon srips: " + questId + "(" + SessionID + "," + questId + "," + step + "," + option + ")");
                        LogHelper.WriteLine(LogLevel.Error, "Nói chuyện với NPC: " + ex.Message);
                        switch (step)
                        {
                            case 2:
                                break;
                            case 4:
                                break;
                            case 1:
                                TaskPromptDataSending(questId, 11, option);
                                break;
                            case 3:
                                TaskPromptDataSending(questId, 31, option);
                                break;
                            case 5:
                                TaskPromptDataSending(questId, 51, option);
                                break;
                        }
                    }

                    return;
            }
        }
        catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi trả nhiệm vụ tại đây: ...." + ex2.Message);
		}
	}

    private bool HandleGroupQuest(int questId, int step, int option)
    {
		switch (questId)
        {
            case 12000:
            case 12001:
			case 12002:
				switch (step)
				{
					case 1:
						// Chọn nhiệm vụ
						// Kiểm tra điều kiện
						var result = GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId);
                        if (result == 13 && option == 1)
                        {
                            if (QuestList.ContainsKey(questId))
                            {
                                HeThongNhacNho("Đã hủy nhiệm vụ!", 10, "Hệ thống nhiệm vụ");
                                QuestList.Remove(questId);
                                TaskPromptDataSending(questId, 41, option);
                                break;
                            }
                            result = 12;
                        }
						TaskPromptDataSending(questId, result, option);
                       
                        
						break;
					case 2:
                        // CHấp nhận nhiệm vụ, cần check có active trước không 
                        if (GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId) != 11)
                        {
						    TaskPromptDataSending(questId, 22, option);
                            break;
                        }
						//SetTaskData(questId, option);
						// Thêm Quest vào danh sách nhiệm vụ của nhân vật
						_mission.ThietLap_NhiemVu(questId,1);
						// Set/Add group quest vào progress
						// TODO:
                        // Gửi thông tin nhận quest tới login server để tạo nhiệm vụ mới
                        World.conn.Transmit($"GROUP_QUEST_ACCEPT|{CharacterName}|{this.GuildId}|{questId}");

						// Gửi response nhận q cho nhân vật
						TaskPromptDataSending(questId, 21, 1);
						break;
					case 3:
						// Từ chối nhận nhiệm vụ
						TaskPromptDataSending(questId, 31, option);
						break;
					case 4:
						// hủy nhiệm vụ
                        if (GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId) != 11)
                        {
						    TaskPromptDataSending(questId, 42, option);
                            break;
                        }
						if (QuestList.ContainsKey(questId))
						{
                            World.conn.Transmit($"GROUP_QUEST_CANCEL|{CharacterName}|{this.GuildId}|{questId}");
							QuestList.Remove(questId);
						}
						TaskPromptDataSending(questId, 41, option);
						break;
					case 5:
						// Kiểm tra đủ điều kiện nhận thưởng không
						if (GroupQuestEvent.Instance.CheckGroupQuestRequirements(this, questId) != 11)
						{
							// Kiểm tra điều kiện nhận q, ko đủ thì ko cho trả
						    TaskPromptDataSending(questId, 52, option);
                            break;
                        }
						// Hoàn thành nhiệm vụ
						TaskPromptDataSending(questId, 51, option);
						CurrentlyOperatingNPC = 0;
						CurrentOperationType = 0;
						break;
				}
				break;
            default:
                return false;
        }

        
		return false;
    }
}