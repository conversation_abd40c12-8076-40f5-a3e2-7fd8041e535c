using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.Quest
{
    /// <summary>
    /// Manager class để quản lý quest system
    /// </summary>
    public static class QuestManager
    {
        /// <summary>
        /// Khởi tạo quest system khi World được khởi động
        /// </summary>
        public static void Initialize()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Đang khởi tạo Quest System...");
                
                // Load dữ liệu quest từ file JSON
                QuestClass.LoadQuestData("quest_data.json");
                
                LogHelper.WriteLine(LogLevel.Info, $"Quest System đã được khởi tạo thành công với {QuestClass.QuestDictionary.Count} quest");
                
                // Log thông tin metadata
                if (QuestClass.Metadata != null)
                {
                    LogHelper.WriteLine(LogLevel.Info, $"Quest Data Version: {QuestClass.Metadata.Version}");
                    LogHelper.WriteLine(LogLevel.Info, $"Export Date: {QuestClass.Metadata.ExportDate}");
                    LogHelper.WriteLine(LogLevel.Info, $"Total Quests: {QuestClass.Metadata.TotalQuests}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi khởi tạo Quest System: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra player có thể nhận quest không
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="questId">ID của quest</param>
        /// <returns>True nếu có thể nhận quest</returns>
        public static bool CanAcceptQuest(Players player, int questId)
        {
            var quest = QuestClass.GetQuest(questId);
            if (quest == null)
            {
                return false;
            }

            // Kiểm tra các yêu cầu để nhận quest
            foreach (var requirement in quest.AcceptRequirements)
            {
                if (!CheckRequirement(player, requirement))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Kiểm tra player có thể hoàn thành quest không
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="questId">ID của quest</param>
        /// <returns>True nếu có thể hoàn thành quest</returns>
        public static bool CanCompleteQuest(Players player, int questId)
        {
            var quest = QuestClass.GetQuest(questId);
            if (quest == null)
            {
                return false;
            }

            // Kiểm tra player có quest này không
            if (!player.QuestList.ContainsKey(questId))
            {
                return false;
            }

            // Kiểm tra các yêu cầu để hoàn thành quest
            foreach (var requirement in quest.CompletionRequirements)
            {
                if (!CheckRequirement(player, requirement))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Kiểm tra một yêu cầu cụ thể
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="requirement">Yêu cầu cần kiểm tra</param>
        /// <returns>True nếu đáp ứng yêu cầu</returns>
        private static bool CheckRequirement(Players player, QuestRequirement requirement)
        {
            switch (requirement.Type?.ToLower())
            {
                case "level":
                    return player.Player_Level >= requirement.Value;
                
                case "item":
                    if (requirement.ItemId.HasValue && requirement.ItemAmount.HasValue)
                    {
                        // Kiểm tra player có đủ item không
                        return HasEnoughItems(player, requirement.ItemId.Value, requirement.ItemAmount.Value);
                    }
                    break;
                
                case "job":
                    return player.Player_Job == requirement.Value;
                
                case "job_level":
                    return player.Player_Job_level >= requirement.Value;
                
                // Thêm các loại requirement khác nếu cần
                default:
                    LogHelper.WriteLine(LogLevel.Warning, $"Loại requirement không được hỗ trợ: {requirement.Type}");
                    return true; // Mặc định cho phép nếu không hiểu requirement
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra player có đủ item không
        /// </summary>
        /// <param name="player">Player object</param>
        /// <param name="itemId">ID của item</param>
        /// <param name="amount">Số lượng cần</param>
        /// <returns>True nếu có đủ item</returns>
        private static bool HasEnoughItems(Players player, long itemId, long amount)
        {
            // TODO: Implement logic kiểm tra item trong inventory
            // Hiện tại return true để test
            return true;
        }

        /// <summary>
        /// Lấy danh sách quest có thể nhận theo level của player
        /// </summary>
        /// <param name="player">Player object</param>
        /// <returns>Danh sách quest có thể nhận</returns>
        public static List<QuestData> GetAvailableQuests(Players player)
        {
            var availableQuests = new List<QuestData>();
            
            foreach (var quest in QuestClass.QuestDictionary.Values)
            {
                // Bỏ qua quest đã nhận
                if (player.QuestList.ContainsKey(quest.QuestId))
                {
                    continue;
                }

                // Kiểm tra có thể nhận quest không
                if (CanAcceptQuest(player, quest.QuestId))
                {
                    availableQuests.Add(quest);
                }
            }

            return availableQuests;
        }

        /// <summary>
        /// Lấy danh sách quest đang thực hiện của player
        /// </summary>
        /// <param name="player">Player object</param>
        /// <returns>Danh sách quest đang thực hiện</returns>
        public static List<QuestData> GetActiveQuests(Players player)
        {
            var activeQuests = new List<QuestData>();
            
            foreach (var questId in player.QuestList.Keys)
            {
                var quest = QuestClass.GetQuest(questId);
                if (quest != null)
                {
                    activeQuests.Add(quest);
                }
            }

            return activeQuests;
        }

        /// <summary>
        /// Lấy thông tin dialog của quest
        /// </summary>
        /// <param name="questId">ID của quest</param>
        /// <param name="dialogType">Loại dialog (accept, refuse, welcomeAccept, welcomeRefuse)</param>
        /// <returns>Nội dung dialog</returns>
        public static string GetQuestDialog(int questId, string dialogType)
        {
            var quest = QuestClass.GetQuest(questId);
            if (quest?.Dialogs == null)
            {
                return string.Empty;
            }

            List<string> dialogs = dialogType.ToLower() switch
            {
                "accept" => quest.Dialogs.Accept,
                "refuse" => quest.Dialogs.Refuse,
                "welcomeaccept" => quest.Dialogs.WelcomeAccept,
                "welcomerefuse" => quest.Dialogs.WelcomeRefuse,
                _ => new List<string>()
            };

            return dialogs.FirstOrDefault() ?? string.Empty;
        }

        /// <summary>
        /// Lấy thông tin stage của quest
        /// </summary>
        /// <param name="questId">ID của quest</param>
        /// <param name="stageId">ID của stage</param>
        /// <returns>QuestStage hoặc null</returns>
        public static QuestStage GetQuestStage(int questId, int stageId)
        {
            var quest = QuestClass.GetQuest(questId);
            return quest?.Stages?.FirstOrDefault(s => s.StageId == stageId);
        }
    }
}
