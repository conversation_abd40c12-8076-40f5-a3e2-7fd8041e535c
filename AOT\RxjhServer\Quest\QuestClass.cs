using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using HeroYulgang.Helpers;

namespace RxjhServer.Quest
{
    /// <summary>
    /// Class chứa dữ liệu quest được load từ quest_data.json
    /// </summary>
    public static class QuestClass
    {
        /// <summary>
        /// Dictionary chứa tất cả quest data, key là questId
        /// </summary>
        public static Dictionary<int, QuestData> QuestDictionary { get; private set; } = new();

        /// <summary>
        /// Thông tin metadata của file quest
        /// </summary>
        public static QuestMetadata Metadata { get; private set; }

        /// <summary>
        /// Load dữ liệu quest từ file quest_data.json
        /// </summary>
        /// <param name="filePath">Đường dẫn đến file quest_data.json</param>
        public static void LoadQuestData(string filePath = "quest_data.json")
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy file quest data: {filePath}");
                    return;
                }

                string jsonContent = File.ReadAllText(filePath);
                var questFile = JsonSerializer.Deserialize<QuestFile>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (questFile?.Quests == null)
                {
                    LogHelper.WriteLine(LogLevel.Error, "Dữ liệu quest không hợp lệ");
                    return;
                }

                // Clear dictionary cũ
                QuestDictionary.Clear();

                // Load metadata
                Metadata = new QuestMetadata
                {
                    Version = questFile.Version,
                    ExportDate = questFile.ExportDate,
                    TotalQuests = questFile.TotalQuests
                };

                // Load quest data vào dictionary
                foreach (var quest in questFile.Quests)
                {
                    if (!QuestDictionary.ContainsKey(quest.QuestId))
                    {
                        QuestDictionary.Add(quest.QuestId, quest);
                    }
                    else
                    {
                        LogHelper.WriteLine(LogLevel.Warning, $"Quest ID {quest.QuestId} đã tồn tại, bỏ qua");
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Đã load thành công {QuestDictionary.Count} quest từ {filePath}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi load quest data: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy quest data theo ID
        /// </summary>
        /// <param name="questId">ID của quest</param>
        /// <returns>QuestData hoặc null nếu không tìm thấy</returns>
        public static QuestData GetQuest(int questId)
        {
            return QuestDictionary.TryGetValue(questId, out var quest) ? quest : null;
        }

        /// <summary>
        /// Kiểm tra quest có tồn tại không
        /// </summary>
        /// <param name="questId">ID của quest</param>
        /// <returns>True nếu quest tồn tại</returns>
        public static bool QuestExists(int questId)
        {
            return QuestDictionary.ContainsKey(questId);
        }

        /// <summary>
        /// Lấy danh sách quest theo level
        /// </summary>
        /// <param name="level">Level của player</param>
        /// <returns>Danh sách quest phù hợp với level</returns>
        public static List<QuestData> GetQuestsByLevel(int level)
        {
            var result = new List<QuestData>();
            foreach (var quest in QuestDictionary.Values)
            {
                if (quest.QuestLevel <= level)
                {
                    result.Add(quest);
                }
            }
            return result;
        }

        /// <summary>
        /// Lấy danh sách quest theo category
        /// </summary>
        /// <param name="category">Category của quest</param>
        /// <returns>Danh sách quest thuộc category</returns>
        public static List<QuestData> GetQuestsByCategory(string category)
        {
            var result = new List<QuestData>();
            foreach (var quest in QuestDictionary.Values)
            {
                if (string.Equals(quest.Category, category, StringComparison.OrdinalIgnoreCase))
                {
                    result.Add(quest);
                }
            }
            return result;
        }
    }

    /// <summary>
    /// Model cho file quest_data.json
    /// </summary>
    public class QuestFile
    {
        public string Version { get; set; }
        public string ExportDate { get; set; }
        public int TotalQuests { get; set; }
        public List<QuestData> Quests { get; set; }
    }

    /// <summary>
    /// Metadata của file quest
    /// </summary>
    public class QuestMetadata
    {
        public string Version { get; set; }
        public string ExportDate { get; set; }
        public int TotalQuests { get; set; }
    }

    /// <summary>
    /// Model cho dữ liệu quest
    /// </summary>
    public class QuestData
    {
        public int QuestId { get; set; }
        public string QuestName { get; set; }
        public int QuestLevel { get; set; }
        public string Description { get; set; }
        public List<QuestRequirement> AcceptRequirements { get; set; } = new();
        public List<QuestRequirement> CompletionRequirements { get; set; } = new();
        public List<QuestReward> Rewards { get; set; } = new();
        public QuestGiver QuestGiver { get; set; }
        public QuestDialogs Dialogs { get; set; }
        public List<QuestStage> Stages { get; set; } = new();
        public bool IsSpecialQuest { get; set; }
        public string Category { get; set; }
        public string FooterExtend { get; set; }
        public QuestUnknownFields UnknownFields { get; set; }
    }

    /// <summary>
    /// Model cho yêu cầu quest
    /// </summary>
    public class QuestRequirement
    {
        public string Type { get; set; }
        public long Value { get; set; }
        public long? ItemId { get; set; }
        public long? ItemAmount { get; set; }
        public int? MapId { get; set; }
        public int? CoordsX { get; set; }
        public int? CoordsY { get; set; }
        public int? CoordsZ { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Model cho phần thưởng quest
    /// </summary>
    public class QuestReward
    {
        public string Type { get; set; }
        public long Value { get; set; }
        public long ItemId { get; set; }
        public long ItemAmount { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Model cho NPC giao quest
    /// </summary>
    public class QuestGiver
    {
        public int NpcId { get; set; }
        public int MapId { get; set; }
        public int CoordsX { get; set; }
        public int CoordsY { get; set; }
        public int CoordsZ { get; set; }
        public int Unknown1 { get; set; }
    }

    /// <summary>
    /// Model cho dialog quest
    /// </summary>
    public class QuestDialogs
    {
        public List<string> Accept { get; set; } = new();
        public List<string> Refuse { get; set; } = new();
        public List<string> WelcomeAccept { get; set; } = new();
        public List<string> WelcomeRefuse { get; set; } = new();
    }

    /// <summary>
    /// Model cho giai đoạn quest
    /// </summary>
    public class QuestStage
    {
        public int StageId { get; set; }
        public string Content { get; set; }
        public QuestGiver Npc { get; set; }
        public List<QuestRequirement> Requirements { get; set; } = new();
        public QuestStageDialogs Dialogs { get; set; }
    }

    /// <summary>
    /// Model cho dialog của giai đoạn quest
    /// </summary>
    public class QuestStageDialogs
    {
        public List<string> ConditionMatch { get; set; } = new();
        public List<string> ConditionNoMatch { get; set; } = new();
    }

    /// <summary>
    /// Model cho các field chưa rõ ý nghĩa
    /// </summary>
    public class QuestUnknownFields
    {
        public int Unknown1 { get; set; }
        public int Unknown2 { get; set; }
        public int Unknown3 { get; set; }
        public int Unknown4 { get; set; }
        public int Unknown5 { get; set; }
        public int Unknown6 { get; set; }
        public int Unknown7 { get; set; }
        public int Unknown8 { get; set; }
        public int Unknown9 { get; set; }
        public int Unknown10 { get; set; }
        public int Unknown11 { get; set; }
    }
}
